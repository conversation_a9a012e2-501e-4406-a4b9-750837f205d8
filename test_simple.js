const addon = require('./build/Debug/bladerf_addon');

console.log('Simple callback test...');

let frameCount = 0;
let readyCount = 0;
let errorCount = 0;

const proc = addon.createIQVideoProcessor(
  () => {
    readyCount++;
    console.log(`onReady called (${readyCount})`);
  },
  (frame) => {
    frameCount++;
    console.log(`onFrame called (${frameCount}) - Frame: ${frame.frameNumber}, Size: ${frame.image.length}`);
    
    if (frameCount >= 2) {
      console.log('Got enough frames, stopping...');
      proc.stop();
      process.exit(0);
    }
  },
  (error) => {
    errorCount++;
    console.log(`onError called (${errorCount}) - Error: ${error}`);
  }
);

console.log('Processor created, waiting for callbacks...');

// Exit after 45 seconds if no frames received
setTimeout(() => {
  console.log(`Timeout: Ready=${readyCount}, Frames=${frameCount}, Errors=${errorCount}`);
  proc.stop();
  process.exit(1);
}, 45000);
