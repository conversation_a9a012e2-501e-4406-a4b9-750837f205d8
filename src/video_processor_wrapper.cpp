#include "video_processor_wrapper.h"
#include <iostream>
#include "wav-stream/wav_stream.h"

namespace VideoDecodingAddon {

using v8::Context;
using v8::Function;
using v8::FunctionCallbackInfo;
using v8::FunctionTemplate;
using v8::Isolate;
using v8::Local;
using v8::Object;
using v8::String;
using v8::Value;
using v8::Boolean;
using v8::Number;

// Static member definition
v8::Persistent<v8::Function> VideoProcessorWrapper::constructor;

VideoProcessorWrapper::VideoProcessorWrapper() = default;

VideoProcessorWrapper::~VideoProcessorWrapper() {
  if (videoProcessor_) {
    videoProcessor_->stop();
  }
  
  // Reset persistent callbacks to avoid memory leaks
  onReadyCallback_.Reset();
  onFrameCallback_.Reset();
  onErrorCallback_.Reset();
}

void VideoProcessorWrapper::Init(const Local<Object> exports) {
  Isolate* isolate = exports->GetIsolate();

  // Prepare constructor template
  const Local<FunctionTemplate> tpl = FunctionTemplate::New(isolate, New);
  tpl->SetClassName(String::NewFromUtf8(isolate, "VideoProcessor").ToLocalChecked());
  tpl->InstanceTemplate()->SetInternalFieldCount(1);

  // Prototype methods
  NODE_SET_PROTOTYPE_METHOD(tpl, "stop", Stop_Prototype);

  constructor.Reset(isolate, tpl->GetFunction(isolate->GetCurrentContext()).ToLocalChecked());
  exports->Set(
    isolate->GetCurrentContext(),
    String::NewFromUtf8(isolate, "VideoProcessor").ToLocalChecked(),
    tpl->GetFunction(isolate->GetCurrentContext()).ToLocalChecked()
  ).Check();
}

void VideoProcessorWrapper::New(const FunctionCallbackInfo<Value>& args) {
  Isolate* isolate = args.GetIsolate();

  if (args.IsConstructCall()) {
    // Invoked as constructor: `new VideoProcessor(...)`
    auto* obj = new VideoProcessorWrapper();
    obj->Wrap(args.This());
    args.GetReturnValue().Set(args.This());
  } else {
    // Invoked as plain function `VideoProcessor(...)`, turn into construct call.
    constexpr int argc = 0;
    Local<Value> argv[1] = {};
    const Local<Function> cons = Local<Function>::New(isolate, constructor);
    const Local<Context> context = isolate->GetCurrentContext();
    const Local<Object> result = cons->NewInstance(context, argc, argv).ToLocalChecked();
    args.GetReturnValue().Set(result);
  }
}

void VideoProcessorWrapper::Stop_Prototype(const FunctionCallbackInfo<Value>& args) {
  Isolate* isolate = args.GetIsolate();
  auto* videoProcessor = Unwrap<VideoProcessorWrapper>(args.Holder());
  videoProcessor->stop();
  args.GetReturnValue().Set(Boolean::New(isolate, true));
}

bool VideoProcessorWrapper::start(
  std::string config, // Configuration string (not used in this demo)
  const Local<Function> onReady,
  const Local<Function> onFrame,
  const Local<Function> onError
) {
  Isolate* isolate = v8::Isolate::GetCurrent();
  // Store the callbacks persistently
  onReadyCallback_.Reset(isolate, onReady);
  onFrameCallback_.Reset(isolate, onFrame);
  onErrorCallback_.Reset(isolate, onError);

  // // Create WAV stream data source
  // // auto wavStream = std::make_unique<WAVIQStream>("samples/spectrozir_6.5mhz.wav", true, true);
  auto wavStream = std::make_unique<WAVIQStream>("samples/recording.wav", true, true);
  // // auto wavStream = std::make_unique<WAVIQStream>("samples/bladerf_2.wav", true, true);

  if (!wavStream->open()) {
    std::cerr << "Failed to open WAV file: " << wavStream->lastError() << std::endl;
    // TODO Call error callback sync
    return false;
  }

  // Create VideoProcessor instance
  videoProcessor_ = std::make_unique<IQVideoProcessor::VideoProcessor>(std::move(wavStream), [this]() {
    std::cout << "Frame ready (C++ callback)" << std::endl;
    while (videoProcessor_->hasNextFrame()) {
      // Here we would normally invoke the onFrameCallback_ to notify JS
      // But to avoid threading issues, we just log to console for this demo
      const auto frame = videoProcessor_->getNextFrame();
      std::cout << "Frame timestamp: " << frame.frameNumber << " ms" << std::endl;
      // TODO Call callback ASYNC
    }
  });

  if (!videoProcessor_->start()) {
    std::cerr << "Failed to start video processor." << std::endl;
    videoProcessor_.reset();
    // TODO Call error callback sync
    return false;
  }

  return true;
}

void VideoProcessorWrapper::stop() {
  if (videoProcessor_) {
    videoProcessor_->stop();
    videoProcessor_.reset();
  }
}

// void VideoProcessorWrapper::InvokeOnReady(double value) {
//   InvokeCallback(onReadyCallback_, value);
// }
//
// void VideoProcessorWrapper::InvokeOnFrame(double value) {
//   InvokeCallback(onFrameCallback_, value);
// }
//
// void VideoProcessorWrapper::InvokeOnError(double value) {
//   InvokeCallback(onErrorCallback_, value);
// }
//
// void VideoProcessorWrapper::InvokeCallback(v8::Persistent<v8::Function>&
// callback, double value) {
//   if (callback.IsEmpty()) {
//     return; // No callback set
//   }
//
//   // Use a safer approach to get the isolate
//   Isolate* isolate = Isolate::GetCurrent();
//   if (!isolate) {
//     std::cerr << "Warning: No current isolate available for callback" <<
//     std::endl; return;
//   }
//
//   v8::HandleScope handleScope(isolate);
//   Local<Context> context = isolate->GetCurrentContext();
//
//   if (context.IsEmpty()) {
//     std::cerr << "Warning: No current context available for callback" <<
//     std::endl; return;
//   }
//
//   try {
//     Local<Function> callbackFunc = Local<Function>::New(isolate,
//     callback); Local<Value> argv[1] = { Number::New(isolate, value) };
//
//     // Call the JavaScript callback function with error handling
//     v8::TryCatch tryCatch(isolate);
//     auto result = callbackFunc->Call(context, v8::Null(isolate), 1,
//     argv);
//
//     if (result.IsEmpty() || tryCatch.HasCaught()) {
//       // Handle potential JavaScript exceptions
//       std::cerr << "Error calling JavaScript callback" << std::endl;
//       if (tryCatch.HasCaught()) {
//         v8::String::Utf8Value exception(isolate,
//         tryCatch.Exception()); std::cerr << "Exception: " <<
//         *exception << std::endl;
//       }
//     }
//   } catch (const std::exception& e) {
//     std::cerr << "C++ exception in callback: " << e.what() << std::endl;
//   } catch (...) {
//     std::cerr << "Unknown exception in callback" << std::endl;
//   }
// }

} // namespace VideoDecodingAddon
