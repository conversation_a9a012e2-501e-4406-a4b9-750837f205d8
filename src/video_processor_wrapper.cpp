#include <node.h>
#include <iostream>
#include "video_processor_wrapper.h"
#include "wav-stream/wav_stream.h"

namespace VideoDecodingAddon {

using v8::Context;
using v8::Function;
using v8::FunctionCallbackInfo;
using v8::FunctionTemplate;
using v8::Isolate;
using v8::Local;
using v8::Object;
using v8::String;
using v8::Value;
using v8::Boolean;
using v8::Number;

// Static member definition
v8::Persistent<v8::Function> VideoProcessorWrapper::constructor;

VideoProcessorWrapper::VideoProcessorWrapper() {

}

VideoProcessorWrapper::~VideoProcessorWrapper() {
  stop();
}

void VideoProcessorWrapper::Init(const Local<Object> exports) {
  Isolate* isolate = exports->GetIsolate();

  // Prepare constructor template
  const Local<FunctionTemplate> tpl = FunctionTemplate::New(isolate, New);
  tpl->SetClassName(String::NewFromUtf8(isolate, "VideoProcessor").ToLocalChecked());
  tpl->InstanceTemplate()->SetInternalFieldCount(1);

  // Prototype methods
  NODE_SET_PROTOTYPE_METHOD(tpl, "stop", Stop_Prototype);

  constructor.Reset(isolate, tpl->GetFunction(isolate->GetCurrentContext()).ToLocalChecked());
  exports->Set(
    isolate->GetCurrentContext(),
    String::NewFromUtf8(isolate, "VideoProcessor").ToLocalChecked(),
    tpl->GetFunction(isolate->GetCurrentContext()).ToLocalChecked()
  ).Check();
}

void VideoProcessorWrapper::New(const FunctionCallbackInfo<Value>& args) {
  Isolate* isolate = args.GetIsolate();

  if (args.IsConstructCall()) {
    // Invoked as constructor: `new VideoProcessor(...)`
    auto* obj = new VideoProcessorWrapper();
    obj->Wrap(args.This());
    args.GetReturnValue().Set(args.This());
  } else {
    // Invoked as plain function `VideoProcessor(...)`, turn into construct call.
    constexpr int argc = 0;
    Local<Value> argv[1] = {};
    const Local<Function> cons = Local<Function>::New(isolate, constructor);
    const Local<Context> context = isolate->GetCurrentContext();
    const Local<Object> result = cons->NewInstance(context, argc, argv).ToLocalChecked();
    args.GetReturnValue().Set(result);
  }
}

void VideoProcessorWrapper::Stop_Prototype(const FunctionCallbackInfo<Value>& args) {
  Isolate* isolate = args.GetIsolate();
  auto* videoProcessor = Unwrap<VideoProcessorWrapper>(args.Holder());
  videoProcessor->stop();
  args.GetReturnValue().Set(Boolean::New(isolate, true));
}

void VideoProcessorWrapper::OnFrame_AsyncCallback(uv_async_t * handle) {
  if (const auto *wrapper = static_cast<VideoProcessorWrapper *>(handle->data)) {
    wrapper->processFramesCallbacks();
  }
}

bool VideoProcessorWrapper::start(
  std::string config, // Configuration string (not used in this demo)
  const Local<Function> onReady,
  const Local<Function> onFrame,
  const Local<Function> onError
) {
  Isolate* isolate = v8::Isolate::GetCurrent();
  // Store the callbacks persistently
  onReadyCallback_.Reset(isolate, onReady);
  onFrameCallback_.Reset(isolate, onFrame);
  onErrorCallback_.Reset(isolate, onError);

  // Initialize UV async handle for thread-safe callbacks
  initializeAsyncCallbacks();

  // // Create WAV stream data source
  // // auto wavStream = std::make_unique<WAVIQStream>("samples/spectrozir_6.5mhz.wav", true, true);
  auto wavStream = std::make_unique<WAVIQStream>("samples/recording.wav", true, true);
  // // auto wavStream = std::make_unique<WAVIQStream>("samples/bladerf_2.wav", true, true);

  if (!wavStream->open()) {
    std::cerr << "Failed to open WAV file: " << wavStream->lastError() << std::endl;
    // TODO Call error callback sync
    return false;
  }

  // Create VideoProcessor instance
  videoProcessor_ = std::make_unique<IQVideoProcessor::VideoProcessor>(std::move(wavStream), [this]() {
    // Signal the Node.js main thread to process frames asynchronously
    if (const auto handle = onFrameAsyncCallbackHandle_.load()) {
      uv_async_send(handle);
    }
  });

  if (!videoProcessor_->start()) {
    std::cerr << "Failed to start video processor." << std::endl;
    videoProcessor_.reset();
    // TODO Call error callback sync
    return false;
  }

  // Call onReady callback immediately after successful start
  if (!onReadyCallback_.IsEmpty()) {
    Isolate* isolate = v8::Isolate::GetCurrent();
    v8::HandleScope handleScope(isolate);
    const Local<Context> context = isolate->GetCurrentContext();
    const Local<Function> callbackFunction = Local<Function>::New(isolate, onReadyCallback_);

    // Call onReady with a simple success message
    Local<Value> argv[1] = { String::NewFromUtf8(isolate, "VideoProcessor started successfully").ToLocalChecked() };
    if (callbackFunction->Call(context, v8::Null(isolate), 1, argv).IsEmpty()) {
      std::cerr << "Error calling JavaScript onReady callback" << std::endl;
    }
  }

  return true;
}

void VideoProcessorWrapper::stop() {
  if (videoProcessor_) {
    videoProcessor_->stop();
    videoProcessor_.reset();
  }

  deinitializeAsyncCallbacks();
  onReadyCallback_.Reset();
  onFrameCallback_.Reset();
  onErrorCallback_.Reset();
}

bool VideoProcessorWrapper::initializeAsyncCallbacks() {
  auto frameAsyncHandle = std::make_unique<uv_async_t>(); // Create instance
  frameAsyncHandle->data = this;
  if (const auto result = uv_async_init(uv_default_loop(), frameAsyncHandle.get(), OnFrame_AsyncCallback); result != 0) {
    return false;
  }
  onFrameAsyncCallbackHandle_.store(frameAsyncHandle.release()); // Transfer ownership
  return true;
}

void VideoProcessorWrapper::deinitializeAsyncCallbacks() {
  if (const auto frameHandle = onFrameAsyncCallbackHandle_.load()) {
    uv_close(reinterpret_cast<uv_handle_t*>(frameHandle), [](uv_handle_t* handle) {
      delete reinterpret_cast<uv_async_t*>(handle);
    });
    onFrameAsyncCallbackHandle_.store(nullptr);
  }
}

void VideoProcessorWrapper::processFramesCallbacks() const {
  // We are in the main Node.js thread here, safe to call V8 APIs
  if (!videoProcessor_ || onFrameCallback_.IsEmpty()) return;

  Isolate* isolate = v8::Isolate::GetCurrent();
  v8::HandleScope handleScope(isolate);
  const Local<Context> context = isolate->GetCurrentContext();
  const Local<Function> callbackFunction = Local<Function>::New(isolate, onFrameCallback_);

  while (videoProcessor_->hasNextFrame()) {
    const auto& frame = videoProcessor_->getNextFrame();
    // Creating JavaScript object for the frame
    const Local<Object> frameObj = Object::New(isolate);
    const Local<v8::ArrayBuffer> buffer = v8::ArrayBuffer::New(isolate, frame.dataSize);
    std::memcpy(buffer->GetBackingStore()->Data(), frame.data.data(), frame.dataSize);
    const Local<v8::Uint8Array> imageBuffer = v8::Uint8Array::New(buffer, 0, frame.dataSize);
    // Setting properties
    frameObj->Set(context, String::NewFromUtf8(isolate, "image").ToLocalChecked(), imageBuffer).Check();
    frameObj->Set(context, String::NewFromUtf8(isolate, "frameNumber").ToLocalChecked(), Number::New(isolate, static_cast<double>(frame.frameNumber))).Check();
    frameObj->Set(context, String::NewFromUtf8(isolate, "width").ToLocalChecked(), Number::New(isolate, static_cast<double>(frame.width))).Check();
    frameObj->Set(context, String::NewFromUtf8(isolate, "height").ToLocalChecked(), Number::New(isolate, static_cast<double>(frame.height))).Check();
    // Call the JavaScript callback function
    Local<Value> argv[1] = { frameObj };

    if (callbackFunction->Call(context, v8::Null(isolate), 1, argv).IsEmpty()) {
      std::cerr << "Error calling JavaScript frame callback" << std::endl;
    }
  }
}

} // namespace VideoDecodingAddon
