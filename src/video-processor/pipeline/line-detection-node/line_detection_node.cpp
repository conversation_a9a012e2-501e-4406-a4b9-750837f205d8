#include "./line_detection_node.h"
#include "../../helpers/helpers.h"

#include "./devtools/data_exporter.hpp"

namespace IQVideoProcessor::Pipeline {

std::vector<TFloat> syncPositionsGraphic;
std::vector<TFloat> orcPositionsGraphic;

LineDetectionNode::LineDetectionNode(const SampleRateType sampleRate) : sampleRate_(sampleRate), segmentSyncDetectionFilter_(sampleRate) {
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  currentSegment_ = &segment; // Store reference to current segment for callbacks

  auto& filteredSegment = segmentSyncDetectionFilter_.process(segment);
  auto [videoSyncPulses, processingEndPosition] = segmentPulsesDetector_.process(filteredSegment);

  // <DEBUGGING>
  if (syncPositionsGraphic.size() != segment.totalSamples) {
    syncPositionsGraphic.resize(segment.totalSamples);
    orcPositionsGraphic.resize(segment.totalSamples);
  }
  std::fill(syncPositionsGraphic.begin(), syncPositionsGraphic.end(), 0);
  std::fill(orcPositionsGraphic.begin(), orcPositionsGraphic.end(), 30);
  for (const auto & detected_video_sync_pulse : videoSyncPulses) {
    // auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.centerPosition) + segment.effectiveOffset - segment.effectiveStartPosition;
    auto fallingPos = static_cast<uint32_t>(detected_video_sync_pulse.fallingFrontPosition) + segment.effectiveOffset;
    auto risingPos = static_cast<uint32_t>(detected_video_sync_pulse.risingFrontPosition) + segment.effectiveOffset;
    auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.absCenterPosition) - segment.effectiveStartPosition + segment.effectiveOffset;
    syncPositionsGraphic[fallingPos] = -35;
    syncPositionsGraphic[centerPos] = -20;
    syncPositionsGraphic[risingPos] = -35;
  }
  DevTools::export_debug_data<TFloat>("LDN", "fronts", segment.segmentIndex, syncPositionsGraphic.data(), syncPositionsGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);
  // </DEBUGGING>

  const auto& standardDetectionResult = videoStandardDetector_.processSegmentSyncPulses(videoSyncPulses);
  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_IN_PROGRESS) {
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_FAILED) {
    if (!standardDetectorRetryCountdown_.running()) {
      standardDetectorRetryCountdown_.reset();
    }
    // Waiting for ~1 second before retrying to detect the standard again
    if (standardDetectorRetryCountdown_.tick(filteredSegment.effectiveSamples)) {
      standardDetectorRetryCountdown_.stop(); // just in case
      videoStandardDetector_.reset();
    }
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_COMPLETE) {
    // Successfully detected the standard, no further action needed
    standardDetectorRetryCountdown_.stop();

    initLineDetectionEventTemplate(standardDetectionResult);
    if (!syncOrchestrator_.initialized()) {
      // Emit STANDARD_DETECTED event immediately when video standard is successfully detected
      currentEvent_.type = LineDetectionEventType::STANDARD_DETECTED;
      currentEvent_.videoStandard = standardDetectionResult.standard;
      currentEvent_.dataSize = 0;
      currentEvent_.lineNumber = 0;
      currentEvent_.frameNumber = 0;
      std::cout << "  >> STANDARD DETECTED: " << standardDetectionResult.standard << std::endl;
      sendOutput(currentEvent_);

      initSyncOrchestrator(standardDetectionResult); // Initialize the orchestrator only once when standard is first detected
    }
  }

  if (syncOrchestrator_.initialized()) {
    syncOrchestrator_.process(videoSyncPulses, processingEndPosition);
    DevTools::export_debug_data<TFloat>("LDN", "orcResults", filteredSegment.segmentIndex, orcPositionsGraphic.data(), orcPositionsGraphic.size());
  }

  return running();
}

void LineDetectionNode::initLineDetectionEventTemplate(const VideoStandardDetector::Result& standardDetectionResult) {
  auto measuredLineSize = static_cast<size_t>(standardDetectionResult.horizontalLineDuration);
  measuredLineSize += measuredLineSize >> 2; // Reserve 25% extra space for safety
  if (currentEvent_.data.size() < measuredLineSize) {
    currentEvent_.data.resize(measuredLineSize);
  }
  currentEvent_.videoStandard = standardDetectionResult.standard;
  currentEvent_.type = LineDetectionEventType::UNKNOWN;
  currentEvent_.odd = false;
  currentEvent_.dataSize = 0;
  currentEvent_.lineNumber = 0;
  currentEvent_.frameNumber = 0;
}

void LineDetectionNode::initSyncOrchestrator(const VideoStandardDetector::Result& standardDetectionResult) {
  syncOrchestrator_.initialize(standardDetectionResult, [this](const SyncOrchestrator::EventData &event) {
    switch (event.type) {
      case SyncOrchestrator::LINE_DETECTED: {
        currentEvent_.type = LineDetectionEventType::LINE_RECEIVED;
        currentEvent_.odd = event.isOdd;
        currentEvent_.lineNumber = event.lineNumber;
        currentEvent_.dataSize = std::ceil(event.toAbsPosition_ - event.fromAbsPosition_);
        // Copy line data from the current segment
        const auto fromIdx = static_cast<uint64_t>(event.fromAbsPosition_) - currentSegment_->effectiveStartPosition + currentSegment_->effectiveOffset;
        std::memcpy(&currentEvent_.data[0], &currentSegment_->data[fromIdx], currentEvent_.dataSize * sizeof(currentSegment_->data[0]));
        std::cout << "  >> LINE DETECTED: " << (event.isOdd ? "ODD" : "EVEN") << " field, line #" << event.lineNumber << " at " << event.fromAbsPosition_ << " to " << event.toAbsPosition_ << " size " << currentEvent_.dataSize << std::endl;
        break;
      }
      case SyncOrchestrator::EQUALIZATION_DETECTED: {
        currentEvent_ .type = LineDetectionEventType::EQUALIZATION;
        currentEvent_.odd = event.isOdd;
        currentEvent_.lineNumber = 0;
        currentEvent_.dataSize = std::ceil(event.toAbsPosition_ - event.fromAbsPosition_);
        // Copy equalization pulse data from the current segment
        const auto fromIdx = static_cast<uint64_t>(event.fromAbsPosition_) - currentSegment_->effectiveStartPosition + currentSegment_->effectiveOffset;
        std::memcpy(&currentEvent_.data[0], &currentSegment_->data[fromIdx], currentEvent_.dataSize * sizeof(currentSegment_->data[0]));
        std::cout << "  >> EQUALIZATION DETECTED: " << (event.isOdd ? "ODD" : "EVEN") << " field at " << event.fromAbsPosition_ << " to " << event.toAbsPosition_ << " size " << currentEvent_.dataSize << std::endl;
        break;
      }
      case SyncOrchestrator::FRAME_BEGIN:
        currentEvent_.type = LineDetectionEventType::FRAME_BEGIN;
        currentEvent_.odd = event.isOdd;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        currentEvent_.frameNumber = event.uninterruptedFrameSeries;
        std::cout << "  >> FRAME BEGIN: " << (event.isOdd ? "ODD" : "EVEN") << " field, " << "uninterrupted frames: " << event.uninterruptedFrameSeries << std::endl;
        break;
      case SyncOrchestrator::FRAME_END:
        currentEvent_.type = LineDetectionEventType::FRAME_END;
        currentEvent_.odd = event.isOdd;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        std::cout << "  >> FRAME END: " << (event.isOdd ? "ODD" : "EVEN") << " field, " << "uninterrupted frames: " << event.uninterruptedFrameSeries << std::endl;
        break;
      case SyncOrchestrator::LOCKED:
        currentEvent_.type = LineDetectionEventType::SYNC_LOCKED;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        std::cout << "  >> SYNC LOCK ACQUIRED" << std::endl;
        break;
      case SyncOrchestrator::LOCK_LOST:
        currentEvent_.type = LineDetectionEventType::SYNC_LOCK_LOST;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        std::cout << "  >> SYNC LOCK LOST" << std::endl;
        break;
      case SyncOrchestrator::UNKNOWN_EVENT:
      default:
        currentEvent_.type = LineDetectionEventType::UNKNOWN;
        currentEvent_.dataSize = 0;
        currentEvent_.lineNumber = 0;
        std::cout << "  >> UNKNOWN EVENT" << std::endl;
        break;
    }

    if (!sendOutput(currentEvent_)) {
      stop();
    }
  });
}

} // namespace IQVideoProcessor::Pipeline
