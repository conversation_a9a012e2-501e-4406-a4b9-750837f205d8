#pragma once

#include "../iiq-stream/iiq_stream.h"
#include "./pipeline/pipeline.h"
#include <memory>
#include <functional>
#include <thread>

namespace IQVideoProcessor {

class VideoProcessor {
public:
  explicit VideoProcessor(std::unique_ptr<IIQStream> stream, std::function<void()> onStateChanged);
  ~VideoProcessor();

  [[nodiscard]] bool start();
  void stop();

  [[nodiscard]] bool hasNextFrame() const;
  [[nodiscard]] Pipeline::FrameCompositionResult& getNextFrame() const;

private:
  bool initialize();

  std::unique_ptr<IIQStream> stream_;
  std::function<void()> onStateChanged_;
  SampleRateType sampleRate_{0}; // Sample rate of the video stream
  size_t maxVideoLineSamples_{0}; // Maximum samples per video line

  std::atomic<bool> running_{false};

  // Pipeline components (pointer-based storage)
  std::unique_ptr<Pipeline::IQAcquisitionNode> acquisitionNode_;
  std::unique_ptr<Pipeline::IQAcquisitionBridge> acquisitionBridge_;
  std::unique_ptr<Pipeline::IQDemodulationNode> demodNode_;
  std::unique_ptr<Pipeline::IQDemodulationLink> demodPassthrough_;
  std::unique_ptr<Pipeline::LineDetectionNode> lineDetectionNode_;
  std::unique_ptr<Pipeline::LineDetectionLink> lineDetectionPassthrough_;
  std::unique_ptr<Pipeline::FrameCompositionNode> frameCompositionNode_;
  std::unique_ptr<Pipeline::FrameCompositionLink> frameCompositionPassthrough_;
  std::unique_ptr<Pipeline::OutputQueueNode> outputQueueNode_;

  // Worker thread
  std::unique_ptr<std::thread> acquisitionThread_;
  std::unique_ptr<std::thread> pipelineMainThread_;

  void connectPipelineNodes();
  void deinitialize();
  void sendStopToPipeline() const;
};

} // namespace IQVideoProcessor
