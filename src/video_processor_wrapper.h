#pragma once
#include <node.h>
#include <node_object_wrap.h>
#include <v8.h>
#include <memory>
#include "video-processor/video_processor.h"

namespace VideoDecodingAddon {

/**
 * VideoProcessorWrapper - Node.js object wrapper for IQVideoProcessor::VideoProcessor
 * This class allows each JavaScript object to own and manage its own C++ VideoProcessor instance
 */
class VideoProcessorWrapper final : public node::ObjectWrap {
public:
  // Node-specific methods / exports / prototypes
  static void Init(v8::Local<v8::Object> exports);
  static void New(const v8::FunctionCallbackInfo<v8::Value>& args);
  static void Stop_Prototype(const v8::FunctionCallbackInfo<v8::Value>& args);
  static v8::Persistent<v8::Function> constructor;

  explicit VideoProcessorWrapper();
  ~VideoProcessorWrapper() override;

  [[nodiscard]] bool start(
    std::string config,
    v8::Local<v8::Function> onReady,
    v8::Local<v8::Function> onFrame,
    v8::Local<v8::Function> onError
  );

  void stop();

private:
  // Instance members
  std::unique_ptr<IQVideoProcessor::VideoProcessor> videoProcessor_;

  // Callback storage for JavaScript callbacks
  v8::Persistent<v8::Function> onReadyCallback_;
  v8::Persistent<v8::Function> onFrameCallback_;
  v8::Persistent<v8::Function> onErrorCallback_;

  // Methods to invoke callbacks
  // void InvokeOnReady(double value);
  // void InvokeOnFrame(double value);
  // void InvokeOnError(double value);
  // void InvokeCallback(v8::Persistent<v8::Function>& callback, double value);
};

} // namespace VideoDecodingAddon
