#pragma once
#include <node_object_wrap.h>
#include <v8.h>
#include <uv.h>
#include <memory>
#include <vector>
#include "video-processor/video_processor.h"

namespace VideoDecodingAddon {

/**
 * VideoProcessorWrapper - Node.js object wrapper for IQVideoProcessor::VideoProcessor
 * This class allows each JavaScript object to own and manage its own C++ VideoProcessor instance
 */
class VideoProcessorWrapper final : public node::ObjectWrap {
public:
  // Node-specific methods / exports / prototypes
  static void Init(v8::Local<v8::Object> exports);
  static void New(const v8::FunctionCallbackInfo<v8::Value>& args);
  static void Stop_Prototype(const v8::FunctionCallbackInfo<v8::Value>& args);
  static void OnFrame_AsyncCallback(uv_async_t *handle);
  static v8::Persistent<v8::Function> constructor;

  explicit VideoProcessorWrapper();
  ~VideoProcessorWrapper() override;

  [[nodiscard]] bool start(
    std::string config,
    v8::Local<v8::Function> onReady,
    v8::Local<v8::Function> onFrame,
    v8::Local<v8::Function> onError
  );

  void stop();

private:
  // Instance members
  std::unique_ptr<IQVideoProcessor::VideoProcessor> videoProcessor_;

  // Callback storage for JavaScript callbacks
  v8::Persistent<v8::Function> onReadyCallback_;
  v8::Persistent<v8::Function> onFrameCallback_;
  v8::Persistent<v8::Function> onErrorCallback_;

  void processFramesCallbacks() const;









  // Methods for async callback processing

  // void processFrameCallbacks();

  // Static methods for UV async callback
  std::atomic<uv_async_t*> onFrameAsyncCallbackHandle_;
  bool initializeAsyncCallbacks();
  void deinitializeAsyncCallbacks();

  // bool asyncHandleInitialized_;
  // uv_async_t* asyncHandle_;
  // static void onAsyncCallback(uv_async_t* handle);

};

} // namespace VideoDecodingAddon
