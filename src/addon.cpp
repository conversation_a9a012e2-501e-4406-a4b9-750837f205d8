#include <node.h>
#include <v8.h>
#include <iostream>
#include "video_processor_wrapper.h"

namespace VideoDecodingAddon {

using v8::Context;
using v8::Function;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Object;
using v8::String;
using v8::Value;
using v8::Boolean;
using v8::Exception;

void createIQVideoProcessor(const FunctionCallbackInfo<Value>& args) {
  Isolate* isolate = args.GetIsolate();
  // Check that we have exactly 3 arguments (callbacks)
  if (args.Length() != 3) {
    isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "Expected exactly 3 arguments: onReady, onFrame, onError callbacks").ToLocalChecked()));
    return;
  }
  // Validate that all arguments are functions
  if (!args[0]->IsFunction() || !args[1]->IsFunction() || !args[2]->IsFunction()) {
    isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "All arguments must be functions").ToLocalChecked()));
    return;
  }

  // Create a new VideoProcessorWrapper instance
  const Local<Context> context = isolate->GetCurrentContext();
  const Local<Function> constructor = Local<Function>::New(isolate, VideoProcessorWrapper::constructor);
  const Local<Object> instance = constructor->NewInstance(context, 0, nullptr).ToLocalChecked();

  try {
    // Prepare the arguments
    const Local<Function> onReady = Local<Function>::Cast(args[0]);
    const Local<Function> onFrame = Local<Function>::Cast(args[1]);
    const Local<Function> onError = Local<Function>::Cast(args[2]);
    // Get the wrapper object and call start
    auto* wrapper = node::ObjectWrap::Unwrap<VideoProcessorWrapper>(instance);
    const auto started = wrapper->start("demo", onReady, onFrame, onError);

    // Return the instance or null if start failed
    started
      ? args.GetReturnValue().Set(instance)
      : args.GetReturnValue().Set(Null(isolate));
  } catch (const std::exception& e) {
    isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, ("VideoProcessor error: " + std::string(e.what())).c_str()).ToLocalChecked()));
  }
}

/**
 * Initialize the addon
 */
void Initialize(const Local<Object> exports) {
  VideoProcessorWrapper::Init(exports);
  NODE_SET_METHOD(exports, "createIQVideoProcessor", createIQVideoProcessor);
}

NODE_MODULE(NODE_GYP_MODULE_NAME, Initialize)

}
