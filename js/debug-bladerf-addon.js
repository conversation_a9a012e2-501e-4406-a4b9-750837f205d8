/**
 * Simplified single-instance video processor test with callback verification
 * This loads the Debug build and focuses on testing one processor at a time
 */

const addon = require('../build/Debug/bladerf_addon');

console.log('🚀 Single VideoProcessor Test with Callback Verification (Debug Build)\n');

// Callback verification state
let callbackStats = {
    onReadyCalled: false,
    onFrameCalled: false,
    onErrorCalled: false,
    frameCount: 0,
    startTime: Date.now()
};

async function runSingleInstanceTest() {
    try {
        console.log('1. Creating single VideoProcessor instance with callback verification:');
        console.log('   📁 Using samples/recording.wav as data source');

        // Define callback functions with verification tracking
        const onReady = (value) => {
            callbackStats.onReadyCalled = true;
            console.log(`   📡 ✅ onReady callback invoked! Value: ${JSON.stringify(value)}`);
        };

        const onFrame = (frame) => {
            callbackStats.onFrameCalled = true;
            callbackStats.frameCount++;
            const elapsed = Date.now() - callbackStats.startTime;

            console.log(`   🎬 ✅ onFrame callback #${callbackStats.frameCount} invoked! (${elapsed}ms elapsed)`);

            if (frame && typeof frame === 'object') {
                console.log(`      Frame Number: ${frame.frameNumber}`);
                console.log(`      Dimensions: ${frame.width}x${frame.height}`);
                console.log(`      Image Buffer Size: ${frame.image ? frame.image.length : 'undefined'} bytes`);

                // Verify JPEG header if image data exists
                if (frame.image && frame.image.length > 4) {
                    const header = Array.from(frame.image.slice(0, 4))
                        .map(b => b.toString(16).padStart(2, '0'))
                        .join(' ');
                    console.log(`      JPEG Header: ${header}`);

                    if (frame.image[0] === 0xFF && frame.image[1] === 0xD8) {
                        console.log(`      ✅ Valid JPEG format detected`);
                    } else {
                        console.log(`      ⚠️  Unexpected image format`);
                    }
                }
            } else {
                console.log(`      ⚠️  Frame object is invalid: ${typeof frame}`);
            }

            // Stop after receiving a few frames for testing
            if (callbackStats.frameCount >= 3) {
                console.log(`\n   🛑 Stopping after ${callbackStats.frameCount} frames for testing purposes`);
                setTimeout(() => {
                    if (videoProcessor) {
                        videoProcessor.stop();
                    }
                }, 100);
            }
        };

        const onError = (error) => {
            callbackStats.onErrorCalled = true;
            console.log(`   ❌ onError callback invoked! Error: ${JSON.stringify(error)}`);
        };

        // Create the VideoProcessor instance
        console.log('   🔧 Creating VideoProcessor instance...');
        const videoProcessor = addon.createIQVideoProcessor(onReady, onFrame, onError);

        if (videoProcessor && typeof videoProcessor === 'object') {
            console.log('   ✅ VideoProcessor instance created successfully');
            console.log('   📦 Returned object type:', typeof videoProcessor);
            console.log('   🔧 Available methods:', Object.getOwnPropertyNames(videoProcessor.__proto__));

            // Wait for callbacks with timeout
            console.log('\n2. Waiting for callbacks (30 second timeout):');
            console.log('   ⏳ Monitoring callback activity...');

            const timeoutPromise = new Promise((resolve) => {
                setTimeout(() => {
                    resolve('timeout');
                }, 30000);
            });

            const callbackPromise = new Promise((resolve) => {
                const checkInterval = setInterval(() => {
                    if (callbackStats.onFrameCalled && callbackStats.frameCount >= 3) {
                        clearInterval(checkInterval);
                        resolve('success');
                    }
                }, 100);
            });

            const result = await Promise.race([timeoutPromise, callbackPromise]);

            // Report callback verification results
            console.log('\n3. Callback Verification Results:');
            console.log(`   📡 onReady called: ${callbackStats.onReadyCalled ? '✅ YES' : '❌ NO'}`);
            console.log(`   🎬 onFrame called: ${callbackStats.onFrameCalled ? '✅ YES' : '❌ NO'} (${callbackStats.frameCount} times)`);
            console.log(`   ❌ onError called: ${callbackStats.onErrorCalled ? '⚠️  YES' : '✅ NO'}`);
            console.log(`   ⏱️  Total elapsed time: ${Date.now() - callbackStats.startTime}ms`);

            if (result === 'timeout') {
                console.log('\n   ⚠️  TIMEOUT: No sufficient callback activity detected within 30 seconds');
                console.log('   🔍 This indicates a potential issue with the async callback mechanism');
            } else {
                console.log('\n   🎉 SUCCESS: Callbacks are working correctly!');
            }

            // Clean shutdown
            console.log('\n4. Cleaning up:');
            if (videoProcessor && typeof videoProcessor.stop === 'function') {
                const stopResult = videoProcessor.stop();
                console.log(`   🛑 VideoProcessor stopped: ${stopResult ? '✅ SUCCESS' : '⚠️  FAILED'}`);
            }

        } else {
            console.log('   ❌ Failed to create VideoProcessor instance');
            console.log('   🔍 Check if the addon was built correctly and WAV file exists');
        }

    } catch (error) {
        console.error('❌ Error during testing:', error.message);
        console.error('   Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the single instance test
runSingleInstanceTest().then(() => {
    console.log('\n📊 Final Summary:');
    console.log(`   Callbacks working: ${callbackStats.onFrameCalled ? '✅ YES' : '❌ NO'}`);
    console.log(`   Frames received: ${callbackStats.frameCount}`);
    console.log(`   Test duration: ${Date.now() - callbackStats.startTime}ms`);

    if (callbackStats.onFrameCalled && callbackStats.frameCount > 0) {
        console.log('\n🎉 Single VideoProcessor test completed successfully!');
        console.log('✅ Async frame callback mechanism is working correctly.');
    } else {
        console.log('\n❌ Single VideoProcessor test failed!');
        console.log('🔍 Async frame callback mechanism needs debugging.');
        process.exit(1);
    }
}).catch(error => {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
});
