/**
 * Debug version of the minimal video processor test
 * This loads the Debug build instead of Release build for debugging
 */

const addon = require('../build/Debug/bladerf_addon');

console.log('🚀 Minimal Video Processor Test (Debug Build)\n');

async function runDebugTest() {
try {
    // 1. Test createIQVideoProcessor function with callbacks (debug build)
    console.log('1. Testing createIQVideoProcessor function with callbacks (debug build):');
    console.log('   Creating VideoProcessor with WAV stream and callback functions...');

    // Define callback functions for debugging
    const onReady = (value) => {
        console.log(`   📡 DEBUG onReady callback invoked with value: ${value}`);
    };

    const onFrame = (value) => {
        console.log(`   🎬 DEBUG onFrame callback invoked with value: ${value}`);
    };

    const onError = (value) => {
        console.log(`   ❌ DEBUG onError callback invoked with value: ${value}`);
    };

    const videoProcessor = addon.createIQVideoProcessor(onReady, onFrame, onError);

    if (videoProcessor && typeof videoProcessor === 'object') {
        console.log('   ✅ VideoProcessor instance created successfully');
        console.log('   📁 Using samples/recording.wav as data source');
        console.log('   🔄 Loop mode enabled with timing simulation');
        console.log('   📦 Returned object type:', typeof videoProcessor);
        console.log('   🔧 Available methods:', Object.getOwnPropertyNames(videoProcessor.__proto__));
    } else {
        console.log('   ❌ Failed to create VideoProcessor');
    }

    console.log('\n2. Testing VideoProcessor stop method:');
    if (videoProcessor && typeof videoProcessor.stop === 'function') {
        const stopResult = videoProcessor.stop();

        if (stopResult) {
            console.log('   ✅ VideoProcessor stopped successfully');
        } else {
            console.log('   ⚠️  VideoProcessor stop returned false');
        }
    } else {
        console.log('   ❌ VideoProcessor instance does not have stop method');
    }

    // 3. Test creating multiple instances with callbacks (debug)
    console.log('\n3. Testing multiple VideoProcessor instances with callbacks (debug):');
    try {
        // Create callback functions for first instance (debug)
        const onReady1 = (value) => console.log(`   📡 DEBUG Instance 1 onReady: ${value}`);
        const onFrame1 = (value) => console.log(`   🎬 DEBUG Instance 1 onFrame: ${value}`);
        const onError1 = (value) => console.log(`   ❌ DEBUG Instance 1 onError: ${value}`);

        // Create callback functions for second instance (debug)
        const onReady2 = (value) => console.log(`   📡 DEBUG Instance 2 onReady: ${value}`);
        const onFrame2 = (value) => console.log(`   🎬 DEBUG Instance 2 onFrame: ${value}`);
        const onError2 = (value) => console.log(`   ❌ DEBUG Instance 2 onError: ${value}`);

        const processor1 = addon.createIQVideoProcessor(onReady1, onFrame1, onError1);
        const processor2 = addon.createIQVideoProcessor(onReady2, onFrame2, onError2);

        if (processor1 && processor2) {
            console.log('   ✅ Multiple instances created successfully');
            console.log('   🔄 Each instance manages its own VideoProcessor and callbacks');

            // Wait a moment to see some callbacks in debug mode
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Stop both instances
            processor1.stop();
            processor2.stop();
            console.log('   ✅ Both instances stopped successfully');
        } else {
            console.log('   ❌ Failed to create multiple instances');
        }
    } catch (multiError) {
        console.log('   ⚠️  Multiple instance test failed:', multiError.message);
    }

} catch (error) {
    console.error('❌ Error during testing:', error.message);
    process.exit(1);
}
}

// Run the debug test
runDebugTest().then(() => {
    console.log('\n🎉 Callback-enhanced video processor test completed! (Debug Build)');
    console.log('📝 The enhanced C++ integration with callback support is working correctly.');
    console.log('🔧 Each JavaScript object now owns and manages its own VideoProcessor instance with callbacks.');
    console.log('🐛 Debug build allows for breakpoint debugging in C++ code.');
}).catch(error => {
    console.error('❌ Debug test failed:', error.message);
    process.exit(1);
});
